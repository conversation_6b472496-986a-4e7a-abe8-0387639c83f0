# 需求转AI提示词技巧

## 概述

将需求文档转化为高效的AI提示词是AI辅助开发的核心技能。本文档将详细介绍如何将结构化的需求转化为AI能够理解并生成高质量代码的提示词。

## 核心原理

### 需求到提示词的转化流程

```
业务需求 → 技术需求 → 结构化描述 → AI提示词 → 代码生成
```

### 转化的关键要素

1. **上下文完整性**：提供足够的背景信息
2. **任务明确性**：清晰定义要完成的任务
3. **约束条件**：明确技术和业务约束
4. **期望输出**：定义预期的代码结构和质量

## 提示词结构框架

### 标准提示词模板

```markdown
# 项目上下文
[项目背景、技术栈、架构信息]

# 任务描述
[具体要实现的功能]

# 技术要求
[技术规范、编码标准、性能要求]

# 输入输出规范
[接口定义、数据格式、参数说明]

# 业务规则
[业务逻辑、验证规则、异常处理]

# 代码示例
[相关代码示例或参考实现]

# 验收标准
[功能验收、质量标准、测试要求]
```

### 提示词层次结构

```
┌─────────────────────────────────────┐
│           全局上下文                 │  ← 项目级别的信息
├─────────────────────────────────────┤
│           模块上下文                 │  ← 当前模块的信息
├─────────────────────────────────────┤
│           任务上下文                 │  ← 具体任务的信息
├─────────────────────────────────────┤
│           技术约束                   │  ← 技术规范和限制
├─────────────────────────────────────┤
│           期望输出                   │  ← 代码结构和质量要求
└─────────────────────────────────────┘
```

## 转化技巧详解

### 1. 上下文信息提取

#### 从需求文档中提取关键信息

**业务上下文：**
- 项目类型和规模
- 目标用户群体
- 核心业务流程
- 关键业务规则

**技术上下文：**
- 技术栈选择
- 架构模式
- 数据库设计
- 第三方集成

**示例转化：**
```markdown
# 原始需求
"开发一个在线教育平台的用户管理功能"

# 转化后的上下文
## 项目上下文
- 项目类型：在线教育平台
- 技术栈：Node.js + Express + MongoDB + React
- 架构模式：RESTful API + 前后端分离
- 用户规模：预计1万+注册用户
- 安全要求：用户数据加密存储，支持JWT认证
```

### 2. 任务分解技巧

#### 将复杂需求分解为具体任务

**分解原则：**
- 单一职责：每个任务只完成一个明确的功能
- 可测试性：任务结果可以被验证
- 独立性：任务之间依赖关系最小
- 可实现性：任务复杂度适中

**示例分解：**
```markdown
# 原始需求
"实现用户管理功能"

# 分解后的任务
## 任务1：用户注册API
- 接收用户注册信息
- 验证数据格式和唯一性
- 密码加密存储
- 返回注册结果

## 任务2：用户登录API
- 验证用户凭据
- 生成JWT令牌
- 记录登录日志
- 返回用户信息和令牌

## 任务3：用户信息管理API
- 获取用户详细信息
- 更新用户资料
- 修改密码
- 注销账户
```

### 3. 技术规范转化

#### 将业务规则转化为技术约束

**转化要点：**
- 数据验证规则
- 业务逻辑约束
- 性能要求
- 安全要求

**示例转化：**
```markdown
# 业务规则
"用户邮箱必须唯一，密码至少8位包含字母和数字"

# 技术约束
## 数据验证规则
- 邮箱格式：符合RFC 5322标准
- 邮箱唯一性：数据库级别唯一约束
- 密码强度：最少8位，包含至少1个字母和1个数字
- 密码存储：使用bcrypt加密，salt rounds = 12

## API响应格式
- 成功：{ success: true, data: {...}, message: "操作成功" }
- 失败：{ success: false, error: "错误码", message: "错误描述" }
```

### 4. 接口规范定义

#### 明确的输入输出规范

**API接口规范模板：**
```markdown
## 接口：POST /api/users/register

### 请求参数
```json
{
  "email": "<EMAIL>",      // 必填，邮箱格式
  "password": "password123",        // 必填，8-20位
  "username": "用户名",             // 必填，2-20位
  "phone": "13800138000"           // 可选，手机号格式
}
```

### 响应格式
```json
// 成功响应
{
  "success": true,
  "data": {
    "userId": "12345",
    "email": "<EMAIL>",
    "username": "用户名",
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "message": "注册成功"
}

// 失败响应
{
  "success": false,
  "error": "EMAIL_EXISTS",
  "message": "邮箱已存在"
}
```
```

## 实战示例

### 示例1：用户注册功能

#### 原始需求
```
用户可以通过邮箱和密码注册账户，系统需要验证邮箱格式和密码强度，
确保邮箱唯一性，注册成功后发送欢迎邮件。
```

#### 转化后的提示词
```markdown
# 项目上下文
- 项目：在线教育平台用户管理系统
- 技术栈：Node.js + Express + MongoDB + Mongoose
- 架构：RESTful API，MVC模式
- 认证方式：JWT
- 邮件服务：使用nodemailer + SMTP

# 任务描述
实现用户注册API接口，包括数据验证、存储和邮件发送功能

# 技术要求
- 使用Express框架创建POST /api/users/register路由
- 使用Mongoose定义User模型
- 密码使用bcrypt加密（salt rounds = 12）
- 邮箱验证使用validator库
- 错误处理使用统一的错误响应格式
- 代码遵循ES6+标准，使用async/await

# 输入输出规范
## 请求体
```json
{
  "email": "string, required, email format",
  "password": "string, required, 8-20 chars, contains letter and number",
  "username": "string, required, 2-20 chars",
  "phone": "string, optional, mobile format"
}
```

## 响应格式
- 成功：201状态码，返回用户基本信息（不含密码）
- 失败：400状态码，返回具体错误信息

# 业务规则
1. 邮箱必须唯一，重复注册返回错误
2. 密码强度：8-20位，至少包含1个字母和1个数字
3. 用户名长度：2-20个字符
4. 注册成功后异步发送欢迎邮件
5. 所有输入数据需要trim处理

# 错误处理
- EMAIL_EXISTS: 邮箱已存在
- INVALID_EMAIL: 邮箱格式错误
- WEAK_PASSWORD: 密码强度不足
- USERNAME_TOO_SHORT: 用户名过短
- VALIDATION_ERROR: 数据验证失败

# 代码结构要求
- 控制器函数：registerUser
- 模型验证：User.schema.pre('save')
- 中间件：validateRegistration
- 工具函数：sendWelcomeEmail
- 错误处理：使用try-catch和自定义错误类

# 验收标准
1. 正常注册流程可以成功完成
2. 重复邮箱注册被正确拒绝
3. 密码强度验证正确工作
4. 欢迎邮件能够正常发送
5. 所有错误情况都有适当的错误响应
6. 代码通过ESLint检查
7. 包含单元测试用例
```

### 示例2：数据查询功能

#### 原始需求
```
管理员可以查看用户列表，支持按邮箱、用户名搜索，
支持分页，可以按注册时间排序。
```

#### 转化后的提示词
```markdown
# 项目上下文
- 项目：在线教育平台管理后台
- 技术栈：Node.js + Express + MongoDB + Mongoose
- 权限控制：基于角色的访问控制（RBAC）
- 前端分页：使用limit和offset

# 任务描述
实现管理员用户列表查询API，支持搜索、分页和排序功能

# 技术要求
- 创建GET /api/admin/users路由
- 使用中间件验证管理员权限
- 使用Mongoose的聚合查询优化性能
- 支持模糊搜索（邮箱、用户名）
- 实现分页逻辑，默认每页20条
- 支持多字段排序

# 输入输出规范
## 查询参数
- page: 页码，默认1
- limit: 每页数量，默认20，最大100
- search: 搜索关键词（邮箱或用户名）
- sortBy: 排序字段，默认createdAt
- sortOrder: 排序方向，asc/desc，默认desc

## 响应格式
```json
{
  "success": true,
  "data": {
    "users": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalUsers": 200,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

# 业务规则
1. 只有管理员可以访问此接口
2. 搜索支持邮箱和用户名的模糊匹配
3. 返回的用户信息不包含密码
4. 支持按注册时间、最后登录时间排序
5. 分页参数验证：page >= 1, limit <= 100

# 性能要求
- 查询响应时间 < 500ms
- 支持数据库索引优化
- 使用聚合管道减少数据传输

# 验收标准
1. 分页功能正确工作
2. 搜索功能准确匹配
3. 排序功能正常
4. 权限验证有效
5. 性能满足要求
6. 包含完整的测试用例
```

## 提示词优化技巧

### 1. 渐进式细化

**第一轮：基础功能**
```markdown
实现用户注册API的基本功能
```

**第二轮：添加验证**
```markdown
在用户注册API基础上，添加邮箱格式验证和密码强度检查
```

**第三轮：完善错误处理**
```markdown
完善用户注册API的错误处理，包括重复邮箱、验证失败等情况
```

### 2. 上下文复用

**建立项目上下文库：**
```markdown
# 项目基础上下文（可复用）
- 技术栈：Node.js + Express + MongoDB
- 架构模式：RESTful API + MVC
- 认证方式：JWT
- 错误处理：统一错误响应格式
- 代码规范：ESLint + Prettier
```

### 3. 模板化管理

**API开发模板：**
```markdown
# API开发提示词模板

## 项目上下文
[复用项目基础信息]

## 接口规范
- 路径：[HTTP方法] [路径]
- 权限：[权限要求]
- 参数：[请求参数]
- 响应：[响应格式]

## 业务逻辑
[具体业务规则]

## 技术实现
[技术要求和约束]

## 验收标准
[测试和质量要求]
```

## 常见问题和解决方案

### 问题1：提示词过长
**解决方案：**
- 使用分层上下文管理
- 建立可复用的上下文模板
- 采用渐进式开发方式

### 问题2：AI理解偏差
**解决方案：**
- 提供具体的代码示例
- 使用明确的技术术语
- 增加验收标准的详细程度

### 问题3：生成代码不一致
**解决方案：**
- 建立统一的代码规范模板
- 使用一致的命名约定
- 定期进行代码审查和重构

## 下一步

掌握需求转提示词技巧后，请继续学习：
1. [AI开发系统化方法论](../03-开发方法论/README.md)
2. [完整案例演示](../04-实战案例/README.md)

---

**关键要点**：高质量的提示词是AI生成高质量代码的前提。投入时间精心设计提示词，能够显著提高开发效率和代码质量。
