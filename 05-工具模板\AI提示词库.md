# AI提示词库

## 概述

本文档提供了一套经过实战验证的AI提示词模板，涵盖软件开发的各个阶段。这些提示词模板可以帮助您更高效地与AI协作，生成高质量的代码和文档。

## 提示词分类

### 🏗️ 架构设计类
### 💻 代码生成类  
### 🧪 测试相关类
### 📚 文档生成类
### 🔧 调试优化类

## 架构设计类提示词

### 1. 系统架构设计

```markdown
# 系统架构设计提示词模板

## 项目上下文
- 项目类型：[Web应用/移动应用/桌面应用/API服务]
- 用户规模：[预期用户数量和并发量]
- 技术栈偏好：[首选的技术栈]
- 部署环境：[云平台/本地部署/混合部署]
- 预算约束：[开发和运维成本考虑]

## 设计要求
请为以下项目设计系统架构：

**项目描述**：[详细的项目描述]

**核心功能**：
1. [功能1描述]
2. [功能2描述]
3. [功能3描述]

**非功能需求**：
- 性能要求：[响应时间、吞吐量等]
- 可扩展性：[预期增长和扩展需求]
- 可用性：[可用性要求和容错需求]
- 安全性：[安全要求和合规需求]

## 输出要求
请提供：
1. 整体架构图（文字描述）
2. 技术栈选择和理由
3. 数据库设计建议
4. 部署架构建议
5. 关键技术决策说明
6. 潜在风险和缓解措施

## 约束条件
- 优先考虑成熟稳定的技术
- 注重开发效率和维护成本
- 考虑团队技术能力
- 符合行业最佳实践
```

### 2. 数据库设计

```markdown
# 数据库设计提示词模板

## 业务上下文
- 业务领域：[电商/教育/社交/企业应用等]
- 数据规模：[预期数据量和增长速度]
- 查询模式：[主要的查询类型和频率]
- 一致性要求：[强一致性/最终一致性]

## 设计要求
请为以下业务场景设计数据库：

**核心实体**：
1. [实体1]：[属性和描述]
2. [实体2]：[属性和描述]
3. [实体3]：[属性和描述]

**业务关系**：
- [实体1] 与 [实体2] 的关系：[一对一/一对多/多对多]
- [实体2] 与 [实体3] 的关系：[关系描述]

**查询需求**：
1. [查询场景1]：[查询描述和频率]
2. [查询场景2]：[查询描述和频率]

## 输出要求
请提供：
1. 数据库类型选择（关系型/NoSQL/混合）
2. 表结构设计（包含字段类型、约束、索引）
3. 关系设计和外键约束
4. 索引策略
5. 分区和分片建议（如需要）
6. 数据迁移和备份策略

## 设计原则
- 遵循数据库设计范式
- 考虑查询性能优化
- 保证数据完整性
- 支持业务扩展需求
```

## 代码生成类提示词

### 1. API接口开发

```markdown
# API接口开发提示词模板

## 项目上下文
- 技术栈：[Node.js + Express / Python + FastAPI / Java + Spring Boot]
- 数据库：[MongoDB / MySQL / PostgreSQL]
- 认证方式：[JWT / OAuth2 / Session]
- API风格：[RESTful / GraphQL]

## 接口需求
请实现以下API接口：

**接口描述**：[接口的业务功能描述]

**HTTP方法和路径**：[GET/POST/PUT/DELETE] /api/[resource]/[action]

**请求参数**：
```json
{
  "param1": "类型和描述",
  "param2": "类型和描述"
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "field1": "类型和描述",
    "field2": "类型和描述"
  },
  "message": "操作成功"
}
```

**业务规则**：
1. [业务规则1]
2. [业务规则2]
3. [错误处理规则]

## 技术要求
- 使用[ORM/ODM]进行数据库操作
- 实现输入参数验证
- 统一的错误处理机制
- 添加适当的日志记录
- 遵循RESTful设计原则
- 包含必要的注释

## 输出要求
请提供：
1. 完整的接口实现代码
2. 数据模型定义（如需要）
3. 输入验证逻辑
4. 错误处理代码
5. 单元测试用例
6. API文档注释

## 代码规范
- 使用[编程语言]的最佳实践
- 遵循项目的命名规范
- 代码结构清晰，职责分离
- 包含详细的注释说明
```

### 2. 前端组件开发

```markdown
# 前端组件开发提示词模板

## 项目上下文
- 框架：[React / Vue / Angular]
- 语言：[JavaScript / TypeScript]
- UI库：[Ant Design / Material-UI / Element UI]
- 状态管理：[Redux / Vuex / NgRx]
- 样式方案：[CSS Modules / Styled Components / SCSS]

## 组件需求
请实现以下前端组件：

**组件名称**：[ComponentName]

**组件功能**：[详细的功能描述]

**Props接口**：
```typescript
interface Props {
  prop1: string;  // 属性描述
  prop2?: number; // 可选属性描述
  onAction: (data: any) => void; // 事件回调
}
```

**组件状态**：
- [状态1]：[状态描述和用途]
- [状态2]：[状态描述和用途]

**交互行为**：
1. [交互1]：[用户操作和系统响应]
2. [交互2]：[用户操作和系统响应]

## 技术要求
- 使用函数式组件和Hooks
- 实现TypeScript类型定义
- 遵循组件设计原则
- 支持响应式设计
- 包含错误边界处理
- 添加适当的注释

## 输出要求
请提供：
1. 完整的组件实现代码
2. TypeScript类型定义
3. 样式文件（CSS/SCSS）
4. 使用示例代码
5. 单元测试用例
6. 组件文档说明

## 设计原则
- 组件职责单一
- 可复用性强
- 易于测试
- 性能优化
- 无障碍访问支持
```

## 测试相关类提示词

### 1. 单元测试生成

```markdown
# 单元测试生成提示词模板

## 测试上下文
- 测试框架：[Jest / Mocha / PyTest / JUnit]
- 断言库：[Jest / Chai / Assert]
- 模拟库：[Jest Mock / Sinon / Mockito]
- 覆盖率工具：[Istanbul / Coverage.py]

## 被测试代码
请为以下代码生成完整的单元测试：

```[编程语言]
[粘贴要测试的代码]
```

## 测试要求
**测试覆盖范围**：
- 正常流程测试
- 边界条件测试
- 异常情况测试
- 错误处理测试

**测试场景**：
1. [场景1]：[输入条件和期望输出]
2. [场景2]：[输入条件和期望输出]
3. [异常场景]：[异常条件和期望行为]

## 技术要求
- 使用[测试框架]编写测试
- 模拟外部依赖
- 测试覆盖率达到90%以上
- 测试用例独立且可重复
- 包含清晰的测试描述

## 输出要求
请提供：
1. 完整的测试文件
2. 测试数据准备代码
3. Mock对象设置
4. 断言验证逻辑
5. 测试清理代码
6. 测试运行说明

## 测试原则
- 测试用例独立
- 测试描述清晰
- 断言具体明确
- 易于维护和理解
```

### 2. 集成测试设计

```markdown
# 集成测试设计提示词模板

## 系统上下文
- 系统架构：[微服务 / 单体应用 / 分层架构]
- 技术栈：[具体的技术栈]
- 数据库：[数据库类型和版本]
- 外部依赖：[第三方服务和API]

## 测试范围
请设计以下模块的集成测试：

**测试模块**：[模块名称和功能]

**集成点**：
1. [模块A] ↔ [模块B]：[集成关系描述]
2. [模块B] ↔ [数据库]：[数据访问测试]
3. [系统] ↔ [外部API]：[外部集成测试]

**测试场景**：
1. [端到端场景1]：[完整的业务流程]
2. [端到端场景2]：[异常处理流程]

## 技术要求
- 使用测试数据库
- 模拟外部服务
- 自动化测试执行
- 测试环境隔离
- 数据清理和重置

## 输出要求
请提供：
1. 集成测试计划
2. 测试环境配置
3. 测试数据准备脚本
4. 完整的测试代码
5. 测试执行脚本
6. 测试报告模板

## 测试策略
- 自底向上集成
- 关键路径优先
- 数据一致性验证
- 性能基准测试
```

## 文档生成类提示词

### 1. API文档生成

```markdown
# API文档生成提示词模板

## 项目信息
- 项目名称：[项目名称]
- API版本：[v1.0.0]
- 基础URL：[https://api.example.com]
- 认证方式：[JWT Bearer Token]

## 文档要求
请为以下API生成完整的文档：

**API接口列表**：
[粘贴API接口代码或描述]

## 文档格式
请使用OpenAPI 3.0格式生成文档，包含：

**基础信息**：
- API概述和用途
- 版本信息和更新日志
- 认证和授权说明
- 错误码定义

**接口详情**：
- 请求方法和路径
- 请求参数（路径、查询、请求体）
- 响应格式和状态码
- 请求/响应示例
- 错误处理说明

## 输出要求
请提供：
1. OpenAPI 3.0规范文档
2. 接口使用示例
3. 错误码对照表
4. 认证流程说明
5. SDK使用指南
6. 变更日志模板

## 文档标准
- 描述清晰准确
- 示例完整可用
- 格式规范统一
- 易于理解和使用
```

### 2. 代码注释生成

```markdown
# 代码注释生成提示词模板

## 代码上下文
- 编程语言：[JavaScript/Python/Java/C#]
- 注释风格：[JSDoc/Sphinx/Javadoc/XML Doc]
- 项目类型：[Web应用/库/框架/工具]

## 注释要求
请为以下代码生成完整的注释：

```[编程语言]
[粘贴需要注释的代码]
```

## 注释内容
**函数/方法注释**：
- 功能描述
- 参数说明（类型、描述、默认值）
- 返回值说明
- 异常说明
- 使用示例
- 注意事项

**类注释**：
- 类的用途和职责
- 主要属性说明
- 重要方法概述
- 使用场景
- 继承关系

**模块注释**：
- 模块功能概述
- 主要导出内容
- 依赖关系
- 使用指南

## 输出要求
请提供：
1. 完整的代码注释
2. 类型定义（如适用）
3. 使用示例
4. 最佳实践说明
5. 相关文档链接

## 注释原则
- 描述准确清晰
- 格式规范统一
- 信息完整有用
- 易于维护更新
```

## 调试优化类提示词

### 1. 代码审查

```markdown
# 代码审查提示词模板

## 审查上下文
- 编程语言：[具体语言]
- 项目类型：[Web/移动/桌面/库]
- 代码规范：[团队或行业标准]
- 性能要求：[具体的性能指标]

## 审查要求
请对以下代码进行全面审查：

```[编程语言]
[粘贴需要审查的代码]
```

## 审查维度
**代码质量**：
- 可读性和可维护性
- 命名规范和代码结构
- 注释质量和文档
- 代码复杂度

**功能正确性**：
- 逻辑正确性
- 边界条件处理
- 错误处理机制
- 业务规则实现

**性能优化**：
- 算法效率
- 内存使用
- 数据库查询优化
- 缓存策略

**安全性**：
- 输入验证
- 权限控制
- 数据加密
- 安全漏洞

## 输出要求
请提供：
1. 问题清单（按严重程度分类）
2. 具体的改进建议
3. 优化后的代码示例
4. 最佳实践建议
5. 相关资源链接

## 审查标准
- 遵循SOLID原则
- 符合团队代码规范
- 考虑可扩展性
- 注重安全性
```

### 2. 性能优化

```markdown
# 性能优化提示词模板

## 性能上下文
- 应用类型：[Web应用/API服务/数据处理]
- 技术栈：[具体技术栈]
- 性能瓶颈：[CPU/内存/IO/网络]
- 目标指标：[响应时间/吞吐量/并发数]

## 优化需求
请对以下代码/系统进行性能优化：

**当前性能问题**：
- [问题1]：[具体的性能表现]
- [问题2]：[性能指标和期望]

**代码/配置**：
```[语言/配置格式]
[粘贴需要优化的代码或配置]
```

**性能目标**：
- 响应时间：[目标时间]
- 吞吐量：[目标QPS/TPS]
- 资源使用：[CPU/内存限制]

## 优化方向
**代码层面**：
- 算法优化
- 数据结构选择
- 缓存策略
- 异步处理

**架构层面**：
- 数据库优化
- 缓存设计
- 负载均衡
- 微服务拆分

## 输出要求
请提供：
1. 性能分析报告
2. 优化方案设计
3. 优化后的代码
4. 性能测试建议
5. 监控指标设置
6. 实施步骤规划

## 优化原则
- 先测量再优化
- 关注关键路径
- 平衡复杂度和性能
- 考虑可维护性
```

## 使用技巧

### 1. 提示词组合使用
- **分层使用**：先用架构设计类，再用代码生成类
- **迭代优化**：使用调试优化类持续改进
- **文档同步**：开发过程中同步生成文档

### 2. 上下文管理
- **保持一致性**：在同一项目中使用一致的上下文信息
- **渐进细化**：从高层设计到具体实现逐步细化
- **版本控制**：将提示词模板纳入版本控制

### 3. 质量控制
- **代码审查**：AI生成的代码必须经过人工审查
- **测试验证**：使用测试类提示词生成测试用例
- **持续优化**：根据使用效果不断优化提示词

## 自定义提示词

### 创建自定义模板
1. **识别需求**：分析团队的特定需求
2. **设计结构**：参考现有模板设计结构
3. **测试验证**：在实际项目中测试效果
4. **持续改进**：根据使用反馈优化模板

### 模板维护
- **版本管理**：为模板建立版本控制
- **文档说明**：为每个模板编写使用说明
- **团队培训**：确保团队成员了解如何使用
- **效果评估**：定期评估模板的使用效果

---

**使用建议**：这些提示词模板是起点，不是终点。根据您的具体项目需求和团队特点，灵活调整和优化这些模板，才能发挥最大价值。
